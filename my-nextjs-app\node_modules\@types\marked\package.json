{"name": "@types/marked", "version": "5.0.2", "description": "TypeScript definitions for Marked", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/marked", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/worr", "githubUsername": "worr"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "CrossR", "url": "https://github.com/CrossR", "githubUsername": "CrossR"}, {"name": "<PERSON>", "url": "https://github.com/mwickett", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/htkzhtm", "githubUsername": "htkzhtm"}, {"name": "<PERSON>", "url": "https://github.com/ezracelli", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>in <PERSON>", "url": "https://github.com/scandinave", "githubUsername": "scandinave"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sarunint", "githubUsername": "sarunint"}, {"name": "<PERSON>", "url": "https://github.com/UziTech", "githubUsername": "UziTech"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Toliak", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jfcere", "githubUsername": "jfcere"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/MykSto", "githubUsername": "MykSto"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/marked"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "16be8ba4196d8d4bf0e717e331a9e711d2dcd9da855c277bd57f9e82f6b55ce6", "typeScriptVersion": "4.5", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}}