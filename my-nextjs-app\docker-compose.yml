version: '3.8'

services:
  nextjs-app:
    build:
      context: .
      dockerfile: dockerfile
    container_name: my-nextjs-app
    ports:
      - "3000:3000"
    # 移除卷挂载，避免覆盖容器中的 node_modules
    # volumes:
    #   - .:/app
    #   - /app/node_modules
    # 使用 .env.local 文件中的环境变量
    env_file:
      - .env.local
    environment:
      - NODE_ENV=development
      # 使用宿主机上的代理
      - HTTP_PROXY=http://host.docker.internal:7890
      - HTTPS_PROXY=http://host.docker.internal:7890
    # 添加extra_hosts，使host.docker.internal指向宿主机
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped