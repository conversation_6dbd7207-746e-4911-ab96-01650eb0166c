<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 Google Agent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(to right, #00d2ff, #3a7bd5);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #3a7bd5;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(to right, #00d2ff, #3a7bd5);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            opacity: 0.9;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #ddd;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #3a7bd5;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试 Google Agent</h1>
        
        <div class="form-group">
            <label for="url">URL:</label>
            <input type="text" id="url" value="https://www.example.com" placeholder="输入要爬取的URL">
        </div>
        
        <div class="form-group">
            <label for="query">查询:</label>
            <input type="text" id="query" value="分析网页内容" placeholder="输入查询内容">
        </div>
        
        <div class="form-group">
            <label for="mode">模式:</label>
            <select id="mode">
                <option value="crawl">爬取网页</option>
                <option value="search">谷歌搜索</option>
            </select>
        </div>
        
        <button onclick="testGoogleAgent()">测试</button>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>处理中，请稍候...</p>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        async function testGoogleAgent() {
            const url = document.getElementById('url').value;
            const query = document.getElementById('query').value;
            const mode = document.getElementById('mode').value;
            const resultDiv = document.getElementById('result');
            const loading = document.getElementById('loading');
            
            if (!url && mode === 'crawl') {
                resultDiv.textContent = '错误: 请输入URL';
                return;
            }
            
            if (!query) {
                resultDiv.textContent = '错误: 请输入查询内容';
                return;
            }
            
            resultDiv.textContent = '';
            loading.style.display = 'block';
            
            try {
                // 直接调用 Python 脚本
                const response = await fetch(`/api/direct-python?url=${encodeURIComponent(url)}&query=${encodeURIComponent(query)}&mode=${mode}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                loading.style.display = 'none';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                loading.style.display = 'none';
                resultDiv.textContent = `错误: ${error.message}`;
                console.error('Error:', error);
                
                // 尝试直接调用 Python 脚本
                resultDiv.textContent += '\n\n尝试直接调用 Python 脚本...\n';
                
                try {
                    const scriptResponse = await fetch(`/api/direct-python?url=${encodeURIComponent(url)}&query=${encodeURIComponent(query)}`);
                    
                    if (!scriptResponse.ok) {
                        throw new Error(`HTTP error! status: ${scriptResponse.status}`);
                    }
                    
                    const scriptData = await scriptResponse.json();
                    resultDiv.textContent += JSON.stringify(scriptData, null, 2);
                } catch (scriptError) {
                    resultDiv.textContent += `\n直接调用 Python 脚本失败: ${scriptError.message}`;
                }
            }
        }
    </script>
</body>
</html>
